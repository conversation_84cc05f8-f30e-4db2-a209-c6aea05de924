### 建表语句提示词

````sql
#角色
你是一个MySQL数据库设计专家，精通产品设计、熟悉产品需求、精通MySQL语句优化

#背景
我要设计一个员工日志管理系统，包括如下功能：
-客户信息管理
-部门信息管理
-员工信息管理
-项目信息管理
-工作日志管理（个人日志管理、全部日志管理
-加班记录管理
-汇总统计管理
-系统管理

#技能
你精通MySQL数据库，精通产品设计，请帮我优化并检查数据库表结构是否存在设计问题，优化后请给出精准，带注释的、完整的MySQL建表语句

#要求：
- 在查询项目日志时，需要对项目名称、工作内容、客户简称、客户名称、员工姓名、项目号进行全文检索
- 在进行项目查询时，需要对项目名称、客户检查、客户名称、项目号进行全文检索
- 员工第一次登录时，强制修改密码
- 可以通过邮件找回登录账号密码
````



### 需求设计提示词

````bash
#角色：
你是一个专业的产品分析及设计专家，擅长产品需求分析、规划及设计

#背景
我要设计一个员工日志管理系统，包括如下功能：
-客户信息管理
-部门信息管理
-员工信息管理
-项目信息管理
-工作日志管理（个人日志管理、全部日志管理）
-加班记录管理
-汇总统计管理
-系统管理

#技能
你精通产品设计，能够非常严禁的根据下面的“数据库初始建表语句”设计一份合理的员工日志管理系统需求规划

#要求：
-系统采用前后端分离技术，后端使用fastapi技术栈（使用uv进行包依赖管理）、前端使用vue3技术栈（使用pnpm依赖包管理）
-页面风格参考Ant Design Pro(网址：https://preview.pro.ant.design/dashboard/analysis）
-前端样式采用最新的tailwind css技术栈
-需要具备登录功能，有友好的登录提示，如登录成功、登录失败等提示
-合理的权限控制：
1 请帮助设计合理的权限及数据权限，我想让普通员工、销售人员都能写日志
2 人员都可以看到自己和其他人员的项目信息、工作日志信息
3 只有管理员可以维护人员信息、部门信息
4 普通员工、销售人员也可以维护项目信息
5 管理员权限最高，可以做所有事情
- 在查询项目日志时，需要对项目名称、工作内容、客户简称、客户名称、员工姓名、项目号进行全文检索
- 在进行项目查询时，需要对项目名称、客户检查、客户名称、项目号进行全文检索
- 员工第一次登录时，强制修改密码
- 可以通过邮件找回登录账号密码
- 加入各种列表导出功能
- 有列表页的地方，默认显示5条数据，可以上下翻页

#输出：
输出完成的产品需求规格说明书，采用markdown格式
````

### 数据库建表语句

```sql
-- 客户信息表：存储客户的基本信息，包括名称、简称等，支持客户信息的管理与检索
CREATE TABLE `customer` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `name` varchar(200) NOT NULL COMMENT '客户名称',
  `abbreviation` varchar(100) NOT NULL COMMENT '客户简称',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `office_address` varchar(500) DEFAULT NULL COMMENT '办公地址',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_name` (`name`) USING BTREE COMMENT '客户名称唯一',
  UNIQUE KEY `uk_customer_abbreviation` (`abbreviation`) USING BTREE COMMENT '客户简称唯一',
  -- 支持客户名称和简称的全文检索，满足项目查询需求
  FULLTEXT INDEX `ft_customer_search` (`name`, `abbreviation`) COMMENT '客户信息全文检索索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客户信息表';

-- 部门信息表：记录公司部门结构，支持部门层级关系，便于组织管理
CREATE TABLE `department` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `parent_id` int DEFAULT NULL COMMENT '父部门ID（自关联，顶级部门为NULL）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_department_name` (`name`) USING BTREE COMMENT '部门名称唯一',
  KEY `idx_department_parent_id` (`parent_id`) USING BTREE COMMENT '父部门ID索引，优化部门层级查询',
  CONSTRAINT `fk_department_parent` FOREIGN KEY (`parent_id`) 
    REFERENCES `department` (`id`) 
    ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='部门信息表';

-- 员工信息表：存储员工的基本信息、登录账号及所属部门等，支持员工身份管理和权限控制
CREATE TABLE `employee` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `login_name` varchar(128) NOT NULL COMMENT '登录账号（使用email）',
  `display_name` varchar(128) NOT NULL COMMENT '员工姓名',
  `password` varchar(128) NOT NULL COMMENT '登录密码（哈希存储）',
  `employee_no` varchar(50) NOT NULL COMMENT '员工编号',
  `is_sales` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为销售人员（0否 1是）',
  `is_admin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为管理员（0否 1是）',
  `department_id` int NOT NULL COMMENT '所属部门ID（关联部门信息表）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '员工状态（1在职 2离职）',
  `is_first_login` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否首次登录（1是 0否），用于强制修改初始密码',
  `last_password_change` datetime DEFAULT NULL COMMENT '最后一次密码修改时间',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_login_name` (`login_name`) USING BTREE COMMENT '登录账号唯一（用于邮件找回密码）',
  UNIQUE KEY `uk_employee_no` (`employee_no`) USING BTREE COMMENT '员工编号唯一',
  KEY `idx_employee_department_id` (`department_id`) USING BTREE COMMENT '部门ID索引，优化部门员工查询',
  KEY `idx_employee_status` (`status`) USING BTREE COMMENT '员工状态索引，优化在职/离职员工筛选',
  -- 支持员工姓名的全文检索，满足项目日志查询需求
  FULLTEXT INDEX `ft_employee_name` (`display_name`) COMMENT '员工姓名全文检索索引',
  CONSTRAINT `fk_employee_department` FOREIGN KEY (`department_id`) 
    REFERENCES `department` (`id`) 
    ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='员工信息表';

-- 项目信息表：记录项目的基本信息、所属客户及负责人等，支持项目全生命周期管理
CREATE TABLE `project` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_number` varchar(50) NOT NULL COMMENT '项目号或合同号',
  `project_type` enum('商机占位','合同项目','运维项目','其他类型') NOT NULL COMMENT '项目类型',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `customer_id` int NOT NULL COMMENT '所属客户ID（关联客户信息表）',
  `manager_id` int NOT NULL COMMENT '项目负责人ID（关联员工信息表）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '项目状态（1进行中 2已结束）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_number` (`project_number`) USING BTREE COMMENT '项目号唯一',
  KEY `idx_project_customer_id` (`customer_id`) USING BTREE COMMENT '客户ID索引，优化客户项目查询',
  KEY `idx_project_manager_id` (`manager_id`) USING BTREE COMMENT '负责人ID索引，优化负责人项目查询',
  KEY `idx_project_status` (`status`) USING BTREE COMMENT '项目状态索引，优化项目状态筛选',
  -- 支持项目名称和项目号的全文检索，满足项目日志和项目查询需求
  FULLTEXT INDEX `ft_project_search` (`project_name`, `project_number`) COMMENT '项目信息全文检索索引',
  CONSTRAINT `fk_project_customer` FOREIGN KEY (`customer_id`) 
    REFERENCES `customer` (`id`) 
    ON DELETE RESTRICT,
  CONSTRAINT `fk_project_manager` FOREIGN KEY (`manager_id`) 
    REFERENCES `employee` (`id`) 
    ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目信息表';

-- 工作日志表：存储员工每日工作记录，包括工作内容、工时及关联项目等，支持工作统计与追溯
CREATE TABLE `worklog` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '工作日志ID',
  `work_date` date NOT NULL COMMENT '工作日期',
  `employee_id` int NOT NULL COMMENT '所属员工ID（关联员工表）',
  `project_id` int NOT NULL COMMENT '所属项目ID（关联项目表）',
  `work_hours` decimal(3,1) NOT NULL COMMENT '工时（0-24之间，精度0.5）',
  `work_content` text NOT NULL COMMENT '工作内容',
  `submit_status` tinyint NOT NULL DEFAULT '0' COMMENT '提交状态（0草稿 1已提交）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_worklog_unique` (`work_date`,`employee_id`,`project_id`) USING BTREE COMMENT '确保同一天内同一员工的同一项目日志唯一',
  KEY `idx_worklog_employee_date` (`employee_id`,`work_date`) USING BTREE COMMENT '员工+日期组合索引，优化个人日志查询',
  KEY `idx_worklog_project_date` (`project_id`,`work_date`) USING BTREE COMMENT '项目+日期组合索引，优化项目日志查询',
  KEY `idx_worklog_submit_status` (`submit_status`) USING BTREE COMMENT '提交状态索引，优化日志审核查询',
  KEY `idx_worklog_work_date` (`work_date`) USING BTREE COMMENT '工作日期索引，优化日期范围查询',
  -- 支持工作内容的全文检索，满足项目日志查询需求
  FULLTEXT INDEX `ft_worklog_content` (`work_content`) COMMENT '工作日志内容全文检索索引',
  CONSTRAINT `fk_worklog_employee` FOREIGN KEY (`employee_id`) 
    REFERENCES `employee` (`id`) 
    ON DELETE RESTRICT,
  CONSTRAINT `fk_worklog_project` FOREIGN KEY (`project_id`) 
    REFERENCES `project` (`id`) 
    ON DELETE RESTRICT,
  CONSTRAINT `chk_work_hours` CHECK (
    (`work_hours` >= 0) AND 
    (`work_hours` <= 24) AND 
    (`work_hours` % 0.5 = 0)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工作日志表';

-- 加班记录表：存储员工加班信息，关联工作日志或独立记录
CREATE TABLE `overtime` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '加班ID',
  `employee_id` int NOT NULL COMMENT '员工ID（关联员工表）',
  `start_time` datetime NOT NULL COMMENT '加班开始时间（精确到年月日分钟）',
  `end_time` datetime NOT NULL COMMENT '加班结束时间（精确到年月日分钟）',
  `overtime_hours` decimal(4,1) GENERATED ALWAYS AS ((timestampdiff(minute,`start_time`,`end_time`)/60)) STORED COMMENT '加班时长（自动计算，单位小时）',
  `project_id` int NOT NULL COMMENT '所属项目ID（关联项目表）',
  `overtime_type` tinyint NOT NULL DEFAULT 1 COMMENT '加班类型（1：工作日加班；2：周末加班；3：法定节假日加班）',
  `overtime_reasons` text NOT NULL COMMENT '加班原因',
  `remarks` varchar(500) DEFAULT NULL COMMENT '加班备注（如“在哪里加班?”）',
  `approver_id` int DEFAULT NULL COMMENT '审批人ID（关联员工表）',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  -- 修改唯一键约束，使用DATE(start_time)代替overtime_date
  UNIQUE KEY `uk_overtime_unique` (`employee_id`,(DATE(`start_time`)),`start_time`,`end_time`) USING BTREE COMMENT '避免同一时段重复记录',
  -- 修改索引，使用DATE(start_time)代替overtime_date
  KEY `idx_overtime_employee_date` (`employee_id`,(DATE(`start_time`))) USING BTREE COMMENT '员工+日期索引，优化查询',
  KEY `idx_overtime_project_date` (`project_id`, (DATE(`start_time`))) USING BTREE COMMENT '项目+日期索引，优化项目加班统计',
  CONSTRAINT `fk_overtime_employee` FOREIGN KEY (`employee_id`) REFERENCES `employee` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_overtime_project` FOREIGN KEY (`project_id`) REFERENCES `project` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_overtime_approver` FOREIGN KEY (`approver_id`) REFERENCES `employee` (`id`) ON DELETE SET NULL,
  CONSTRAINT `chk_overtime_time` CHECK ((`end_time` > `start_time`)),
  -- 删除引用不存在列的约束，或修改为使用现有列
  CONSTRAINT `chk_overtime_hours_limit` CHECK ((`overtime_hours` > 0) AND (`overtime_hours` <= 24))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='加班记录表（包含约束：结束时间必须晚于开始时间）';

-- 密码重置表：用于邮件找回密码功能
CREATE TABLE `password_reset` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `employee_id` int NOT NULL COMMENT '员工ID（关联员工表）',
  `reset_token` varchar(255) NOT NULL COMMENT '密码重置令牌（唯一）',
  `expires_at` datetime NOT NULL COMMENT '令牌过期时间',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用（0未使用 1已使用）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_reset_token` (`reset_token`) USING BTREE COMMENT '重置令牌唯一',
  KEY `idx_employee_id` (`employee_id`) USING BTREE COMMENT '员工ID索引，优化查询',
  KEY `idx_token_expire` (`reset_token`,`expires_at`) USING BTREE COMMENT '令牌+过期时间索引，验证令牌有效性',
  CONSTRAINT `fk_password_reset_employee` FOREIGN KEY (`employee_id`) 
    REFERENCES `employee` (`id`) 
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='密码重置记录表';

-- 系统管理表：存储系统配置参数
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键（唯一）',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_desc` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用（0禁用 1启用）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`) USING BTREE COMMENT '配置键唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统配置表';
```
