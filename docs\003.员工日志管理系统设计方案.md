# 员工日志管理系统设计方案

### 1.1 项目背景与目标

员工日志管理系统是企业级应用中不可或缺的一部分，它能够帮助企业高效管理员工日常工作记录、项目信息以及相关统计数据。本系统旨在为企业提供一个功能全面、操作便捷的日志管理平台，实现客户信息、部门信息、员工信息、项目信息以及工作日志等核心业务的数字化管理，提高工作效率和数据准确性[(6)](https://developer.aliyun.com/article/1660638)。

### 1.2 系统架构设计

本系统采用前后端分离的架构模式，后端采用 FastAPI 技术栈，前端采用 Vue3 技术栈，页面风格参考 Ant Design Pro，前端样式采用 Tailwind CSS 技术栈。这种架构设计具有以下优势：

*   **前后端分离**：提高开发效率，便于团队分工协作，同时增强系统的可维护性和可扩展性[(2)](https://zuplo.com/blog/2025/05/27/emerging-tools-frameworks)

*   **高性能**：FastAPI 基于 Python，采用异步编程，性能优异，能够处理高并发请求[(2)](https://zuplo.com/blog/2025/05/27/emerging-tools-frameworks)

*   **开发效率高**：Vue3 的组合式 API 和响应式系统使开发更加高效，Ant Design Pro 提供了丰富的组件库和布局方案[(11)](https://gitee.com/openstudy123/antdv-pro)

*   **美观易用**：Ant Design Pro 的设计风格符合现代企业级应用的审美标准，Tailwind CSS 提供了灵活的样式解决方案[(16)](https://blog.csdn.net/qq_44808710/article/details/147947920)

系统架构图如下：

```
+----------------+     +----------------+
|    浏览器      |<---->|     前端       |
+----------------+     +----------------+
|   (Vue3)       |     | (Ant Design Pro)|
+----------------+     |    (Tailwind)   |
                        +----------------+
                            |   HTTP/HTTPS
                            v
+----------------+     +----------------+
|     后端       |<---->|    数据库      |
|   (FastAPI)    |     |   (PostgreSQL) |
+----------------+     +----------------+
```

### 1.3 技术选型与优势

**后端技术栈**：

*   **FastAPI**

**前端技术栈**：

*   **Vue3**：最新的 Vue.js 版本

*   **Ant Design Pro**：基于 Ant Design 的企业级中后台前端 / 设计解决方案，提供了丰富的组件和布局模式

*   **Tailwind CSS**：实用优先的 CSS 框架，能够快速构建美观、响应式的用户界面

*   **Vue Router**：官方路由管理器，支持 SPA 应用的导航管理

*   **Pinia**：Vue3 的官方状态管理库

### 2.1 客户信息管理模块

客户信息管理模块主要实现对客户信息的增删改查功能，并且根据用户角色不同设置不同的访问权限。

**功能点列表**：

*   **添加客户信息**：管理员可以添加新的客户信息，包括客户基本资料、联系方式、业务需求等

*   **编辑客户信息**：管理员可以修改已有的客户信息，确保数据的准确性和时效性

*   **删除客户信息**：管理员可以删除不再需要的客户信息，但需谨慎操作，避免数据丢失

*   **查看客户信息**：管理员和普通用户都可以查看客户信息，但普通用户只能查看不能修改

*   **客户信息搜索**：提供多种搜索条件，方便快速查找特定客户信息

*   **客户信息导出**：支持将客户信息导出为 Excel 文件，便于备份和外部共享

**权限控制**：

*   管理员拥有所有操作权限（增删改查）

*   普通用户仅拥有查看权限

### 2.2 部门信息管理模块

部门信息管理模块负责管理企业内部的部门结构，为整个系统提供组织架构基础。

**功能点列表**：

*   **添加部门信息**：管理员可以创建新的部门，并设置部门的基本信息和负责人

*   **编辑部门信息**：管理员可以修改部门的名称、负责人、职能描述等信息

*   **删除部门信息**：管理员可以删除部门，但需注意，如果部门下有员工，需要先处理员工的部门归属问题

*   **查看部门信息**：管理员和普通用户都可以查看部门信息，但普通用户只能查看不能修改

*   **部门结构展示**：以树形结构展示企业的部门层次关系，直观明了

*   **部门信息统计**：统计各部门的员工人数、项目数量等信息

**权限控制**：

*   管理员拥有所有操作权限（增删改查）

*   普通用户仅拥有查看权限

### 2.3 员工信息管理模块

员工信息管理模块是系统的核心模块之一，负责管理企业员工的基本信息和账号信息。

**功能点列表**：

*   **添加员工信息**：管理员可以添加新员工的基本信息，包括姓名、性别、出生日期、入职时间等

*   **编辑员工信息**：管理员可以修改员工的基本信息，但员工的登录账号信息通常由员工本人修改

*   **删除员工信息**：管理员可以删除员工信息，但需谨慎操作，建议改为离职状态而非物理删除

*   **查看员工信息**：管理员可以查看所有员工的信息，普通用户只能查看自己的信息

*   **员工账号管理**：管理员可以创建、重置员工的登录账号和密码

*   **员工权限管理**：管理员可以设置员工的角色和权限，控制其对系统功能的访问级别

**权限控制**：

*   管理员拥有所有操作权限（增删改查）

*   普通用户仅能查看和修改自己的个人信息

### 2.4 项目信息管理模块

项目信息管理模块负责管理企业的各类项目信息，为项目管理提供支持。

**功能点列表**：

*   **添加项目信息**：普通用户可以添加新项目的基本信息，如项目名称、开始时间、结束时间、项目负责人等

*   **编辑项目信息**：普通用户可以编辑自己负责的项目信息，管理员可以编辑所有项目信息

*   **删除项目信息**：普通用户可以删除自己创建的项目，管理员可以删除任何项目

*   **查看项目信息**：所有用户都可以查看项目的基本信息，但详细信息可能需要更高权限

*   **项目进度跟踪**：记录项目的进度状态，如未开始、进行中、已完成等

*   **项目成员管理**：管理项目的参与人员，包括添加、移除成员和设置成员角色

*   **项目文档管理**：上传和管理项目相关的文档资料

**权限控制**：

*   管理员拥有所有操作权限（增删改查）

*   普通用户可以维护和查看项目信息，但部分高级操作需要管理员权限

### 2.5 工作日志管理模块

工作日志管理模块是系统的核心功能之一，员工可以记录每日的工作内容和成果。

**功能点列表**：

*   **撰写工作日志**：普通用户可以撰写每日的工作日志，包括工作内容、完成情况、遇到的问题等

*   **编辑工作日志**：用户可以编辑自己撰写的日志，通常只能编辑当天或最近几天的日志

*   **删除工作日志**：用户可以删除自己撰写的日志，管理员可以删除任何日志

*   **查看工作日志**：所有用户都可以查看自己和他人的工作日志，但可能需要根据权限设置访问范围

*   **日志分类标签**：支持为日志添加标签，方便分类和检索

*   **日志搜索功能**：提供多种搜索条件，如日期范围、关键词、项目等，方便快速查找日志

*   **日志统计分析**：对日志内容进行统计分析，生成工作时长、项目投入等报表

**权限控制**：

*   管理员拥有所有操作权限（增删改查）

*   普通用户可以撰写、编辑和删除自己的日志，查看所有日志

### 2.6 加班记录管理模块

加班记录管理模块用于记录和管理员工的加班信息，为企业的考勤管理提供支持。

**功能点列表**：

*   **添加加班记录**：普通用户可以记录自己的加班信息，包括加班日期、开始时间、结束时间、加班原因等

*   **编辑加班记录**：用户可以编辑自己提交的加班记录，但通常只能在审批前进行修改

*   **删除加班记录**：用户可以删除自己提交的加班记录，管理员可以删除任何加班记录

*   **查看加班记录**：所有用户都可以查看自己和他人的加班记录，但权限可能不同

*   **加班申请审批**：支持多级审批流程，主管可以审批下属的加班申请

*   **加班统计分析**：统计员工的加班时长、加班频率等信息，生成相关报表

**权限控制**：

*   管理员拥有所有操作权限（增删改查）

*   普通用户可以添加、编辑和删除自己的加班记录，查看所有加班记录

### 2.7 汇总统计管理模块

汇总统计管理模块对系统中的各类数据进行综合分析和统计，为管理层提供决策支持。

**功能点列表**：

*   **员工工作统计**：统计员工的工作时长、项目参与情况、任务完成情况等

*   **项目进度统计**：分析项目的进度状态、资源分配、成本预算等信息

*   **部门绩效评估**：基于员工和项目数据，评估各部门的工作绩效

*   **加班情况统计**：统计各部门、各员工的加班情况，分析加班原因和趋势

*   **客户业务分析**：分析客户的业务需求、合作历史、价值贡献等信息

*   **数据可视化**：将统计结果以图表、报表等形式展示，直观易懂

*   **自定义报表**：支持用户根据自己的需求自定义统计报表和分析条件

**权限控制**：

*   主要由管理员和管理层使用，但部分基础统计信息可能对普通用户开放

### 2.8 系统管理模块

系统管理模块负责系统的基础配置和管理，确保系统的正常运行和安全性。

**功能点列表**：

*   **用户角色管理**：定义和管理系统中的用户角色和权限分配

*   **权限设置**：配置不同角色对系统功能的访问权限

*   **系统参数配置**：管理系统的各种配置参数，如日志保留期限、报表格式等

*   **数据备份与恢复**：定期备份系统数据，并支持数据恢复功能

*   **系统日志管理**：记录系统的操作日志、错误日志等，便于故障排查和安全审计

*   **系统监控**：监控系统的运行状态、性能指标、资源使用情况等

*   **版本更新管理**：管理系统的版本更新和升级，确保系统功能的持续优化

**权限控制**：

*   仅管理员拥有系统管理模块的访问权限

###  3.0 后端核心功能实现细节

1.  **登录功能**：

*   支持用户名 / 密码登录

*   返回访问令牌和刷新令牌

*   首次登录强制修改密码

*   密码复杂度验证

2. **密码找回**：

*   通过邮件发送密码重置链接

*   重置令牌有效期控制

*   安全的密码重置流程

3. **数据导出**：

*   使用 pandas 或 openpyxl 库生成 Excel 文件

*   支持自定义导出字段和格式

*   文件缓存和下载链接有效期控制

4. **全文检索**：

*   使用 PostgreSQL 的全文搜索功能

*   对项目日志和项目信息建立全文索引

*   支持模糊查询和相关度排序

*   5.**关联表查询**：

*   使用 SQLAlchemy 的关联查询功能

*   优化复杂查询的性能

*   避免 N+1 查询问题

### 3.1 前端核心功能实现细节

1.  **登录页面**：

*   支持用户名 / 密码登录

*   密码强度提示

*   首次登录强制修改密码功能

*   记住密码功能

*   密码找回链接

2. **数据表格**：

*   使用 Ant Design Vue 的表格组件

*   支持分页、排序、筛选功能

*   可自定义列显示

*   行操作菜单（编辑、删除等）

3. **表单验证**：

*   使用 Ant Design Vue 的表单验证
*   支持自定义验证规则
*   实时验证和提交验证
*   错误消息提示

4. **文件导出**：

*   使用前端库生成 Excel 文件

*   支持自定义导出内容和格式

*   文件下载进度提示

*   导出文件的命名规范

5. **全文搜索**：

*   使用 Vue 的计算属性和 watch 实现实时搜索

*   搜索结果高亮显示

*   搜索历史记录

*   高级搜索条件（日期范围、类型筛选等）

### 3.2 权限控制设计

系统采用基于角色的访问控制（RBAC）模型，实现细粒度的权限管理。

**角色定义**：

*   **管理员**：拥有系统的所有权限，包括管理用户、角色、权限，以及所有业务数据的增删改查[(9)](https://www.iesdouyin.com/share/video/7481727595281665334/?did=MS4wLjABAAAANwkJuWIRFOzg5uCpDRpMj4OX-QryoDgn-yYlXQnRwQQ\&from_aid=1128\&from_ssr=1\&iid=MS4wLjABAAAANwkJuWIRFOzg5uCpDRpMj4OX-QryoDgn-yYlXQnRwQQ\&mid=7452148730796738577\&region=\&scene_from=dy_open_search_video\&share_sign=UDFIj4NZyyDj7r1eEtrUt917mfg10zQnNdgmG5jIpHY-\&share_version=280700\&titleType=title\&ts=1753541361\&u_code=0\&video_share_track_ver=\&with_sec_did=1)

*   **普通用户**：可以查看和维护自己的数据，部分公共数据的查看权限，以及提交申请等基本功能

*   **主管**：除了普通用户的权限外，还可以审批下属的申请，查看部门统计数据等

**权限控制策略**：

1.  **页面级权限**：通过路由元信息控制页面的访问权限

1.  **数据级权限**：在 API 层面根据用户角色和数据所有权过滤返回的数据

1.  **行级操作权限**：根据用户角色和数据所有权控制特定行的操作权限


**权限管理流程**：

1.  用户登录时，系统根据用户角色加载其拥有的权限列表

2.  系统根据权限列表动态生成菜单和可操作的功能

3.  用户访问页面时，系统检查其是否具有该页面的访问权限

4.  用户执行操作时，系统检查其是否具有该操作的执行权限

5.  管理员可以在系统管理模块中配置角色和权限的对应关系

### 4.1 总体设计风格

系统采用 Ant Design Pro 的设计风格，结合 Tailwind CSS 实现响应式布局和美观的用户界面。

**设计原则**：

*   **简洁直观**：界面元素简洁明了，操作流程直观易懂

*   **一致性**：保持界面元素、操作流程和交互方式的一致性

*   **可访问性**：确保所有用户都能方便地使用系统功能

*   **高效性**：减少不必要的操作步骤，提高工作效率

*   **可视化**：使用图表和报表直观展示数据信息

**界面布局**：

系统采用左侧导航菜单 + 顶部导航栏的布局方式，主要包括以下几个部分：

1.  **Logo 区域**：位于页面左上角，显示系统名称和 Logo

2.  **导航菜单**：位于左侧，提供系统功能的导航入口

3.  **顶部导航栏**：位于页面顶部，显示用户信息、通知、设置等

4.  **面包屑导航**：位于内容区域顶部，显示当前页面的路径

5.  **内容区域**：页面的主要区域，显示具体的功能内容

6.  **页脚**：位于页面底部，显示版权信息和系统信息

### 4.2 关键界面设计

#### 4.2.1 登录页面

登录页面是用户进入系统的入口，设计应简洁明了，突出登录功能。

**设计要点**：

*   **登录表单**：包含用户名、密码输入框和登录按钮

*   **密码找回链接**：位于表单下方，提供密码重置入口

*   **首次登录提示**：首次登录用户强制修改密码的提示

*   **记住密码功能**：可选记住用户名和密码

*   **第三方登录**：预留第三方登录接口（如企业微信、钉钉等）

**页面布局**：

```
+----------------------+
|      登录页面         |
+----------------------+
|       系统Logo        |
|----------------------|
| 用户名：[__________]  |
| 密码：  [__________]  |
|----------------------|
| [记住密码] [登录]      |
|----------------------|
| 忘记密码？             |
|----------------------|
| 首次登录？请修改密码    |
+----------------------+
```

#### 4.2.2 主界面

主界面是用户登录后的默认页面，提供系统的主要功能入口和常用信息。

**设计要点**：

*   **导航菜单**：提供系统各功能模块的导航链接

*   **顶部导航栏**：显示用户信息、通知、设置等

*   **快捷入口**：常用功能的快速访问按钮

*   **数据概览**：关键数据的统计信息（如项目数量、任务完成率、加班情况等）

*   **最近操作**：显示用户最近的操作记录

*   **系统通知**：显示系统公告、提醒等信息

**页面布局**：

```
+----------------------+
|      系统Logo        | 用户信息 |
+----------------------+-----------------+
| 导航菜单              | 面包屑导航       |
+----------------------+-----------------+
|                      | 数据概览区域     |
|                      +-----------------+
|                      | 最近操作区域     |
|                      +-----------------+
|                      | 系统通知区域     |
+----------------------+-----------------+
|          页脚         |
+----------------------+
```

#### 4.2.3 客户信息管理界面

客户信息管理界面用于维护客户的基本信息，支持增删改查功能。

**设计要点**：

*   **搜索栏**：提供多种搜索条件，方便快速查找客户

*   **新建按钮**：用于添加新客户

*   **客户列表**：显示客户的基本信息，支持排序、分页

*   **操作按钮**：对每个客户进行编辑、删除等操作

*   **详情视图**：显示客户的详细信息和相关项目

**页面布局**：

```
+----------------------+
| 客户信息管理         | [搜索框] [新建] |
+----------------------+-----------------+
| 客户列表：           |
|----------------------|
| 姓名 | 联系方式 | 业务类型 | 操作 |
|------|----------|----------|------|
| 客户1| 138**** | 软件开发 | [编辑][删除] |
| 客户2| 139**** | 系统集成 | [编辑][删除] |
|------|----------|----------|------|
| 分页器：1/5 [上一页][1][2][3][4][5][下一页] |
+----------------------+-----------------+
| 客户详情：           |
|----------------------|
| 基本信息 | 联系记录 | 项目记录 |
|----------------------|
| 详细信息区域         |
+----------------------+
```

#### 4.2.4 工作日志管理界面

工作日志管理界面用于记录和查看员工的工作日志。

**设计要点**：

*   **日志撰写区域**：提供富文本编辑器或结构化表单记录日志内容

*   **日志列表**：按日期显示日志摘要，支持快速导航

*   **日志搜索**：按日期、关键词、项目等条件搜索日志

*   **日志统计**：显示日志记录的统计信息，如连续记录天数、日均字数等

*   **日志分类**：支持标签或分类管理，方便组织和查找日志

**页面布局**：

```
+----------------------+
| 工作日志管理         | [日期选择器] [搜索框] |
+----------------------+-----------------+
| 日志撰写区域：       |
|----------------------|
| [标题输入框]         |
|----------------------|
| [富文本编辑器]       |
|----------------------|
| [保存] [发布]        |
+----------------------+-----------------+
| 日志列表：           |
|----------------------|
| 日期 | 标题 | 字数 | 操作 |
|------|------|------|------|
| 2023-05-01 | 项目A开发进展 | 1200 | [查看][编辑][删除] |
| 2023-04-30 | 需求分析会议 | 800 | [查看][编辑][删除] |
|------|------|------|------|
| 分页器：1/5 [上一页][1][2][3][4][5][下一页] |
+----------------------+-----------------+
| 日志统计：           |
|----------------------|
| 连续记录天数：15天   |
| 日均字数：1024字     |
+----------------------+
```

#### 4.2.5 汇总统计界面

汇总统计界面用于展示系统中各类数据的统计分析结果。

**设计要点**：

*   **统计图表**：使用折线图、柱状图、饼图等可视化方式展示数据

*   **统计维度**：支持按部门、时间、项目等维度进行统计

*   **自定义报表**：允许用户根据自己的需求自定义统计条件和显示方式

*   **数据导出**：支持将统计结果导出为 Excel、PDF 等格式

*   **下钻分析**：支持点击图表或数据项进行下钻分析，查看更详细的数据

**页面布局**：

```
+----------------------+
| 汇总统计             | [统计维度选择] [时间范围选择] |
+----------------------+-----------------+
| 统计图表区域：       |
|----------------------|
| [折线图] [柱状图] [饼图] |
|----------------------|
| 图表说明：           |
+----------------------+-----------------+
| 统计数据表格：       |
|----------------------|
| 部门 | 总工时 | 平均工时 | 加班时长 |
|------|--------|----------|----------|
| 研发部 | 860h | 8.6h | 120h |
| 市场部 | 780h | 7.8h | 30h |
|------|--------|----------|----------|
| [导出Excel] |
+----------------------+
```

### 5.1 数据库建表语句

```sql
-- 客户信息表：存储客户的基本信息，包括名称、简称等，支持客户信息的管理与检索
CREATE TABLE `customer` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `name` varchar(200) NOT NULL COMMENT '客户名称',
  `abbreviation` varchar(100) NOT NULL COMMENT '客户简称',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `office_address` varchar(500) DEFAULT NULL COMMENT '办公地址',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_name` (`name`) USING BTREE COMMENT '客户名称唯一',
  UNIQUE KEY `uk_customer_abbreviation` (`abbreviation`) USING BTREE COMMENT '客户简称唯一',
  -- 支持客户名称和简称的全文检索，满足项目查询需求
  FULLTEXT INDEX `ft_customer_search` (`name`, `abbreviation`) COMMENT '客户信息全文检索索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客户信息表';

-- 部门信息表：记录公司部门结构，支持部门层级关系，便于组织管理
CREATE TABLE `department` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `parent_id` int DEFAULT NULL COMMENT '父部门ID（自关联，顶级部门为NULL）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_department_name` (`name`) USING BTREE COMMENT '部门名称唯一',
  KEY `idx_department_parent_id` (`parent_id`) USING BTREE COMMENT '父部门ID索引，优化部门层级查询',
  CONSTRAINT `fk_department_parent` FOREIGN KEY (`parent_id`) 
    REFERENCES `department` (`id`) 
    ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='部门信息表';

-- 员工信息表：存储员工的基本信息、登录账号及所属部门等，支持员工身份管理和权限控制
CREATE TABLE `employee` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `login_name` varchar(128) NOT NULL COMMENT '登录账号（使用email）',
  `display_name` varchar(128) NOT NULL COMMENT '员工姓名',
  `password` varchar(128) NOT NULL COMMENT '登录密码（哈希存储）',
  `employee_no` varchar(50) NOT NULL COMMENT '员工编号',
  `is_sales` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为销售人员（0否 1是）',
  `is_admin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为管理员（0否 1是）',
  `department_id` int NOT NULL COMMENT '所属部门ID（关联部门信息表）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '员工状态（1在职 2离职）',
  `is_first_login` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否首次登录（1是 0否），用于强制修改初始密码',
  `last_password_change` datetime DEFAULT NULL COMMENT '最后一次密码修改时间',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_login_name` (`login_name`) USING BTREE COMMENT '登录账号唯一（用于邮件找回密码）',
  UNIQUE KEY `uk_employee_no` (`employee_no`) USING BTREE COMMENT '员工编号唯一',
  KEY `idx_employee_department_id` (`department_id`) USING BTREE COMMENT '部门ID索引，优化部门员工查询',
  KEY `idx_employee_status` (`status`) USING BTREE COMMENT '员工状态索引，优化在职/离职员工筛选',
  -- 支持员工姓名的全文检索，满足项目日志查询需求
  FULLTEXT INDEX `ft_employee_name` (`display_name`) COMMENT '员工姓名全文检索索引',
  CONSTRAINT `fk_employee_department` FOREIGN KEY (`department_id`) 
    REFERENCES `department` (`id`) 
    ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='员工信息表';

-- 项目信息表：记录项目的基本信息、所属客户及负责人等，支持项目全生命周期管理
CREATE TABLE `project` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_number` varchar(50) NOT NULL COMMENT '项目号或合同号',
  `project_type` enum('商机占位','合同项目','运维项目','其他类型') NOT NULL COMMENT '项目类型',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `customer_id` int NOT NULL COMMENT '所属客户ID（关联客户信息表）',
  `manager_id` int NOT NULL COMMENT '项目负责人ID（关联员工信息表）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '项目状态（1进行中 2已结束）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_number` (`project_number`) USING BTREE COMMENT '项目号唯一',
  KEY `idx_project_customer_id` (`customer_id`) USING BTREE COMMENT '客户ID索引，优化客户项目查询',
  KEY `idx_project_manager_id` (`manager_id`) USING BTREE COMMENT '负责人ID索引，优化负责人项目查询',
  KEY `idx_project_status` (`status`) USING BTREE COMMENT '项目状态索引，优化项目状态筛选',
  -- 支持项目名称和项目号的全文检索，满足项目日志和项目查询需求
  FULLTEXT INDEX `ft_project_search` (`project_name`, `project_number`) COMMENT '项目信息全文检索索引',
  CONSTRAINT `fk_project_customer` FOREIGN KEY (`customer_id`) 
    REFERENCES `customer` (`id`) 
    ON DELETE RESTRICT,
  CONSTRAINT `fk_project_manager` FOREIGN KEY (`manager_id`) 
    REFERENCES `employee` (`id`) 
    ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目信息表';

-- 工作日志表：存储员工每日工作记录，包括工作内容、工时及关联项目等，支持工作统计与追溯
CREATE TABLE `worklog` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '工作日志ID',
  `work_date` date NOT NULL COMMENT '工作日期',
  `employee_id` int NOT NULL COMMENT '所属员工ID（关联员工表）',
  `project_id` int NOT NULL COMMENT '所属项目ID（关联项目表）',
  `work_hours` decimal(3,1) NOT NULL COMMENT '工时（0-24之间，精度0.5）',
  `work_content` text NOT NULL COMMENT '工作内容',
  `submit_status` tinyint NOT NULL DEFAULT '0' COMMENT '提交状态（0草稿 1已提交）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_worklog_unique` (`work_date`,`employee_id`,`project_id`) USING BTREE COMMENT '确保同一天内同一员工的同一项目日志唯一',
  KEY `idx_worklog_employee_date` (`employee_id`,`work_date`) USING BTREE COMMENT '员工+日期组合索引，优化个人日志查询',
  KEY `idx_worklog_project_date` (`project_id`,`work_date`) USING BTREE COMMENT '项目+日期组合索引，优化项目日志查询',
  KEY `idx_worklog_submit_status` (`submit_status`) USING BTREE COMMENT '提交状态索引，优化日志审核查询',
  KEY `idx_worklog_work_date` (`work_date`) USING BTREE COMMENT '工作日期索引，优化日期范围查询',
  -- 支持工作内容的全文检索，满足项目日志查询需求
  FULLTEXT INDEX `ft_worklog_content` (`work_content`) COMMENT '工作日志内容全文检索索引',
  CONSTRAINT `fk_worklog_employee` FOREIGN KEY (`employee_id`) 
    REFERENCES `employee` (`id`) 
    ON DELETE RESTRICT,
  CONSTRAINT `fk_worklog_project` FOREIGN KEY (`project_id`) 
    REFERENCES `project` (`id`) 
    ON DELETE RESTRICT,
  CONSTRAINT `chk_work_hours` CHECK (
    (`work_hours` >= 0) AND 
    (`work_hours` <= 24) AND 
    (`work_hours` % 0.5 = 0)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工作日志表';

-- 加班记录表：存储员工加班信息，关联工作日志或独立记录
CREATE TABLE `overtime` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '加班ID',
  `employee_id` int NOT NULL COMMENT '员工ID（关联员工表）',
  `start_time` datetime NOT NULL COMMENT '加班开始时间（精确到年月日分钟）',
  `end_time` datetime NOT NULL COMMENT '加班结束时间（精确到年月日分钟）',
  `overtime_hours` decimal(4,1) GENERATED ALWAYS AS ((timestampdiff(minute,`start_time`,`end_time`)/60)) STORED COMMENT '加班时长（自动计算，单位小时）',
  `project_id` int NOT NULL COMMENT '所属项目ID（关联项目表）',
  `overtime_type` tinyint NOT NULL DEFAULT 1 COMMENT '加班类型（1：工作日加班；2：周末加班；3：法定节假日加班）',
  `overtime_reasons` text NOT NULL COMMENT '加班原因',
  `remarks` varchar(500) DEFAULT NULL COMMENT '加班备注（如“在哪里加班?”）',
  `approver_id` int DEFAULT NULL COMMENT '审批人ID（关联员工表）',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  -- 修改唯一键约束，使用DATE(start_time)代替overtime_date
  UNIQUE KEY `uk_overtime_unique` (`employee_id`,(DATE(`start_time`)),`start_time`,`end_time`) USING BTREE COMMENT '避免同一时段重复记录',
  -- 修改索引，使用DATE(start_time)代替overtime_date
  KEY `idx_overtime_employee_date` (`employee_id`,(DATE(`start_time`))) USING BTREE COMMENT '员工+日期索引，优化查询',
  KEY `idx_overtime_project_date` (`project_id`, (DATE(`start_time`))) USING BTREE COMMENT '项目+日期索引，优化项目加班统计',
  CONSTRAINT `fk_overtime_employee` FOREIGN KEY (`employee_id`) REFERENCES `employee` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_overtime_project` FOREIGN KEY (`project_id`) REFERENCES `project` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_overtime_approver` FOREIGN KEY (`approver_id`) REFERENCES `employee` (`id`) ON DELETE SET NULL,
  CONSTRAINT `chk_overtime_time` CHECK ((`end_time` > `start_time`)),
  -- 删除引用不存在列的约束，或修改为使用现有列
  CONSTRAINT `chk_overtime_hours_limit` CHECK ((`overtime_hours` > 0) AND (`overtime_hours` <= 24))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='加班记录表（包含约束：结束时间必须晚于开始时间）';

-- 密码重置表：用于邮件找回密码功能
CREATE TABLE `password_reset` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `employee_id` int NOT NULL COMMENT '员工ID（关联员工表）',
  `reset_token` varchar(255) NOT NULL COMMENT '密码重置令牌（唯一）',
  `expires_at` datetime NOT NULL COMMENT '令牌过期时间',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用（0未使用 1已使用）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_reset_token` (`reset_token`) USING BTREE COMMENT '重置令牌唯一',
  KEY `idx_employee_id` (`employee_id`) USING BTREE COMMENT '员工ID索引，优化查询',
  KEY `idx_token_expire` (`reset_token`,`expires_at`) USING BTREE COMMENT '令牌+过期时间索引，验证令牌有效性',
  CONSTRAINT `fk_password_reset_employee` FOREIGN KEY (`employee_id`) 
    REFERENCES `employee` (`id`) 
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='密码重置记录表';

-- 系统管理表：存储系统配置参数
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键（唯一）',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_desc` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用（0禁用 1启用）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`) USING BTREE COMMENT '配置键唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统配置表';
```

### 6.1 启动后端服务（在backend目录下）

uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

### 6.2 启动前端服务（在frontend目录下）

pnpm run dev

### 6.3. 访问系统

前端：http://localhost:3000

后端API：http://localhost:8000

API文档：http://localhost:8000/docs

